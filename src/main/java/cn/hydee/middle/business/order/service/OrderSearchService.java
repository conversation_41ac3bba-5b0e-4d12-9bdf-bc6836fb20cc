package cn.hydee.middle.business.order.service;

import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.lang.dto.api.PageDTO;

public interface OrderSearchService {

  PageDTO<OrderSimpleRes> orderSearchPage(OrderPageSearchReq req);

  OrderDomainRelatedRes orderSearchByOrderNo(OrderSearchByOrderNoReq req);
}
