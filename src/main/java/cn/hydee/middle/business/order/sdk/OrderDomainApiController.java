package cn.hydee.middle.business.order.sdk;

import cn.hydee.middle.business.order.service.OrderSearchService;
import com.yxt.domain.order.order_query.OrderQueryDomainApi;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class OrderDomainApiController implements OrderQueryDomainApi {

  @Autowired
  private OrderSearchService orderSearchService;

  @Override
  public ResponseBase<PageDTO<OrderSimpleRes>> orderSearchPage(OrderPageSearchReq req) {
    return ResponseBase.success(orderSearchService.orderSearchPage(req));
  }

  @Override
  public ResponseBase<OrderDomainRelatedRes> orderSearchByOrderNo(OrderSearchByOrderNoReq req) {
    return  ResponseBase.success(orderSearchService.orderSearchByOrderNo(req));
  }
}

