package cn.hydee.middle.business.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.ServiceTypeEnum;
import cn.hydee.middle.business.order.configuration.ThreadPoolConfig;
import cn.hydee.middle.business.order.dto.essync.OrderLogES;
import cn.hydee.middle.business.order.dto.essync.QryActionLogReq;
import cn.hydee.middle.business.order.dto.essync.QryActionLogRes;
import cn.hydee.middle.business.order.entity.ErpBillInfo;
import cn.hydee.middle.business.order.entity.MedicalTraceCode;
import cn.hydee.middle.business.order.entity.OrderAssembleCommodityRelation;
import cn.hydee.middle.business.order.entity.OrderCommodityDetailCostPrice;
import cn.hydee.middle.business.order.entity.OrderDeliveryAddress;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import cn.hydee.middle.business.order.entity.OrderPickInfo;
import cn.hydee.middle.business.order.entity.OrderPrescription;
import cn.hydee.middle.business.order.feign.HydeeEsSyncClient;
import cn.hydee.middle.business.order.mapper.ErpBillInfoMapper;
import cn.hydee.middle.business.order.mapper.InnerStoreDictionaryMapper;
import cn.hydee.middle.business.order.mapper.MedicalTraceCodeMapper;
import cn.hydee.middle.business.order.mapper.OrderAssembleCommodityRelationMapper;
import cn.hydee.middle.business.order.mapper.OrderCommodityDetailCostPriceMapper;
import cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import cn.hydee.middle.business.order.mapper.OrderDetailMapper;
import cn.hydee.middle.business.order.mapper.OrderGiftInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPayInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPickInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper;
import cn.hydee.middle.business.order.service.OrderSearchService;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.order.common.utils.YxtDateUtils;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class OrderSearchServiceImpl implements OrderSearchService {

  @Autowired
  private OrderInfoMapper orderInfoMapper;

  @Autowired
  private OrderDetailMapper orderDetailMapper;

  @Autowired
  private OrderGiftInfoMapper orderGiftInfoMapper;

  @Autowired
  private OrderPayInfoMapper orderPayInfoMapper;

  @Autowired
  private OrderPickInfoMapper orderPickInfoMapper;

  @Autowired
  private MedicalTraceCodeMapper medicalTraceCodeMapper;

  @Autowired
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

  @Autowired
  private OrderAssembleCommodityRelationMapper orderAssembleCommodityRelationMapper;

  @Autowired
  private OrderCommodityDetailCostPriceMapper orderCommodityDetailCostPriceMapper;

  @Autowired
  private OrderDeliveryAddressMapper orderDeliveryAddressMapper;

  @Autowired
  private OrderPrescriptionMapper orderPrescriptionMapper;

  @Autowired
  private ErpBillInfoMapper erpBillInfoMapper;

  @Autowired
  private InnerStoreDictionaryMapper innerStoreDictionaryMapper;

  @Autowired
  private HydeeEsSyncClient hydeeEsSyncClient;

  @Resource(name = ThreadPoolConfig.ORDER_SYNC_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;

  @Override
  public PageDTO<OrderSimpleRes> orderSearchPage(OrderPageSearchReq req) {

    // 构建查询条件
    LambdaQueryWrapper<OrderInfo> queryWrapper = Wrappers.lambdaQuery();

    if(CollUtil.isNotEmpty(req.getOrderNoList())){
      queryWrapper.in(OrderInfo::getOrderNo, req.getOrderNoList());
    }
    
    // 网店编码列表过滤
    if (CollUtil.isNotEmpty(req.getClientCodeList())) {
      queryWrapper.in(OrderInfo::getClientCode, req.getClientCodeList());
    }

    // 门店编码列表过滤
    if (CollUtil.isNotEmpty(req.getOrgCodeList())) {
      queryWrapper.in(OrderInfo::getOrganizationCode, req.getOrgCodeList());
    }

    // 平台编码过滤
    if (StringUtils.isNotBlank(req.getPlatformCode())) {
      queryWrapper.eq(OrderInfo::getThirdPlatformCode, req.getPlatformCode());
    }

    // 服务模式过滤
    if (StringUtils.isNotBlank(req.getServiceMode())) {
      queryWrapper.eq(OrderInfo::getServiceMode, req.getServiceMode());
    }

    if(CollUtil.isNotEmpty(req.getOrderStatusList())){
      queryWrapper.in(OrderInfo::getOrderState, req.getOrderStatusList());
    }

    // 订单创建时间范围过滤
    if (req.getStartOrderCreatedDate() != null) {
      Date startDate = Date.from(req.getStartOrderCreatedDate().atStartOfDay(ZoneId.systemDefault())
          .toInstant());
      queryWrapper.ge(OrderInfo::getCreated, startDate);
    }

    if (req.getEndOrderCreatedDate() != null) {
      // 结束日期设置为当天的23:59:59
      Date endDate = Date.from(req.getEndOrderCreatedDate().atTime(LocalTime.MAX)
          .atZone(ZoneId.systemDefault()).toInstant());
      queryWrapper.le(OrderInfo::getCreated, endDate);
    }

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(OrderInfo::getCreated);

    // 执行分页查询
    Page<OrderInfo> page = new Page<>(req.getCurrentPage(), req.getPageSize());
    IPage<OrderInfo> orderInfoPage = orderInfoMapper.selectPage(page, queryWrapper);

    // 构建返回结果
    PageDTO<OrderSimpleRes> pageDTO = new PageDTO<>();
    pageDTO.setCurrentPage(req.getCurrentPage());
    pageDTO.setPageSize(req.getPageSize());
    pageDTO.setTotalCount(orderInfoPage.getTotal());
    pageDTO.setTotalPage(orderInfoPage.getPages());
    // 提取订单号列表
    List<OrderSimpleRes> simpleOrderList = orderInfoPage.getRecords().stream()
        .map(orderInfo -> {
          OrderSimpleRes response = new OrderSimpleRes();
          response.setOrderNo(StrUtil.toStringOrNull(orderInfo.getOrderNo()));
          response.setPlatformCode(orderInfo.getThirdPlatformCode());
          response.setBusinessType(orderInfo.getServiceMode());
          return response;
        }).collect(Collectors.toList());
    pageDTO.setData(simpleOrderList);
    return pageDTO;
  }

  @Override
  public OrderDomainRelatedRes orderSearchByOrderNo(OrderSearchByOrderNoReq req) {
    OrderDomainRelatedRes orderDomainRelatedRes = new OrderDomainRelatedRes();
    //订单信息
    OrderInfo orderInfo = orderInfoMapper.selectOne(Wrappers.<OrderInfo>lambdaQuery()
        .eq(OrderInfo::getOrderNo, req.getOrderNo()));
    if (ObjectUtil.isNull(orderInfo)) {
      throw new YxtBizException(StrUtil.format("订单[{}]不存在", req.getOrderNo()));
    }
    orderDomainRelatedRes.setOrderInfo(BeanUtil.toBean(orderInfo, com.yxt.order.common.base_order_dto.OrderInfo.class));

    AtomicReference<OrderDomainRelatedRes> responseAtomic = new AtomicReference<>(orderDomainRelatedRes);

    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    //订单明细
    futureList.add(CompletableFuture.runAsync(() -> {
      List<OrderDetail> orderDetailList = orderDetailMapper.selectList(Wrappers.<OrderDetail>lambdaQuery()
          .eq(OrderDetail::getOrderNo, req.getOrderNo()));
      responseAtomic.get()
          .setDetailList(BeanUtil.copyToList(orderDetailList, com.yxt.order.common.base_order_dto.OrderDetail.class));
      //订单拣货信息
      List<OrderPickInfo> orderPickInfos = orderPickInfoMapper.selectList(Wrappers.<OrderPickInfo>lambdaQuery()
          .in(OrderPickInfo::getOrderDetailId, orderDetailList.stream().map(OrderDetail::getId)
              .collect(Collectors.toList())));
      responseAtomic.get()
          .setOrderPickInfoList(BeanUtil.copyToList(orderPickInfos, com.yxt.order.common.base_order_dto.OrderPickInfo.class));
    }, orderSearchSubPool));

    //订单支付信息
    futureList.add(CompletableFuture.runAsync(() -> {
      OrderPayInfo orderPayInfo = orderPayInfoMapper.selectOne(Wrappers.<OrderPayInfo>lambdaQuery()
          .eq(OrderPayInfo::getOrderNo, req.getOrderNo()).last(" limit 1 "));
      responseAtomic.get()
          .setOrderPayInfo(BeanUtil.toBean(orderPayInfo, com.yxt.order.common.base_order_dto.OrderPayInfo.class));
    }, orderSearchSubPool));
    //订单追溯码
    futureList.add(CompletableFuture.runAsync(() -> {
      List<MedicalTraceCode> medicalTraceCodes = medicalTraceCodeMapper.selectList(Wrappers.<MedicalTraceCode>lambdaQuery()
          .eq(MedicalTraceCode::getOrderNo, req.getOrderNo()).eq(MedicalTraceCode::getType, 0));
      responseAtomic.get()
          .setMedicalTraceCodeList(BeanUtil.copyToList(medicalTraceCodes, com.yxt.order.common.base_order_dto.MedicalTraceCode.class));
    }, orderSearchSubPool));
    //订单配送
    futureList.add(CompletableFuture.runAsync(() -> {
      OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectOne(Wrappers.<OrderDeliveryRecord>lambdaQuery()
          .eq(OrderDeliveryRecord::getOrderNo, req.getOrderNo()).last(" limit 1 "));
      responseAtomic.get()
          .setOrderDeliveryRecord(BeanUtil.toBean(orderDeliveryRecord, com.yxt.order.common.base_order_dto.OrderDeliveryRecord.class));
    }, orderSearchSubPool));
    //订单组合商品关系
    futureList.add(CompletableFuture.runAsync(() -> {
      List<OrderAssembleCommodityRelation> orderAssembleCommodityRelations = orderAssembleCommodityRelationMapper.selectList(Wrappers.<OrderAssembleCommodityRelation>lambdaQuery()
          .eq(OrderAssembleCommodityRelation::getThirdOrderNo, orderInfo.getThirdOrderNo()));
      responseAtomic.get()
          .setOrderAssembleCommodityRelationList(BeanUtil.copyToList(orderAssembleCommodityRelations, com.yxt.order.common.base_order_dto.OrderAssembleCommodityRelation.class));
    }, orderSearchSubPool));
    //订单商品明细成本价
    futureList.add(CompletableFuture.runAsync(() -> {
      List<OrderCommodityDetailCostPrice> orderCommodityDetailCostPrices = orderCommodityDetailCostPriceMapper.selectList(Wrappers.<OrderCommodityDetailCostPrice>lambdaQuery()
          .eq(OrderCommodityDetailCostPrice::getOrderNo, req.getOrderNo()));
      responseAtomic.get()
          .setOrderCommodityDetailCostPriceList(BeanUtil.copyToList(orderCommodityDetailCostPrices, com.yxt.order.common.base_order_dto.OrderCommodityDetailCostPrice.class));
    }, orderSearchSubPool));
    //订单收货地址
    futureList.add(CompletableFuture.runAsync(() -> {
      OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderInfo.getOrderNo());
      responseAtomic.get()
          .setOrderDeliveryAddress(BeanUtil.toBean(orderDeliveryAddress, com.yxt.order.common.base_order_dto.OrderDeliveryAddress.class));
    }, orderSearchSubPool));
    //订单日志
    futureList.add(CompletableFuture.runAsync(() -> {
      List<OrderLogES> orderLogESList = new ArrayList<>();
      QryActionLogReq qryActionLogReq = new QryActionLogReq();
      qryActionLogReq.setItemId(req.getOrderNo());
      qryActionLogReq.setPageSize(1000);
      qryActionLogReq.setType(ServiceTypeEnum.HYDEE_BUSINESS_ORDER_LOG);
      int currentPage = 1;
      while (true) {
        qryActionLogReq.setCurrentPage(currentPage);
        ResponseBase<QryActionLogRes> res = hydeeEsSyncClient.qryActionLog(qryActionLogReq);
        if (ObjectUtil.isNull(res.getData()) || CollUtil.isEmpty(res.getData().getData())) {
          break;
        }
        res.getData().getData().stream().sorted().forEach((data) -> {
          OrderLogES orderLogES = new OrderLogES();
          BeanUtils.copyProperties(data, orderLogES);
          orderLogES.setOrderState(Integer.valueOf(data.getChildItemId()));
          orderLogES.setErpState(Integer.valueOf(data.getFilePath()));
          orderLogES.setCreateTime(LocalDateTimeUtil.formatNormal(YxtDateUtils.parse(StrUtil.toStringOrNull(data.getCreateTime()))));
          orderLogESList.add(orderLogES);
        });
        currentPage++;
        if (res.getData().getData().size() < qryActionLogReq.getPageSize()) {
          break;
        }
      }
      responseAtomic.get().setLogList(BeanUtil.copyToList(orderLogESList, com.yxt.order.common.base_order_dto.OrderLogES.class));
    }, orderSearchSubPool));
    //处方信息
    futureList.add(CompletableFuture.runAsync(() -> {
      List<OrderPrescription> orderPrescriptions = orderPrescriptionMapper.selectList(Wrappers.<OrderPrescription>lambdaQuery()
          .eq(OrderPrescription::getOrderNo, req.getOrderNo()));
      responseAtomic.get()
          .setOrderPrescriptionList(BeanUtil.copyToList(orderPrescriptions, com.yxt.order.common.base_order_dto.OrderPrescription.class));
    }, orderSearchSubPool));
    //下账信息
    futureList.add(CompletableFuture.runAsync(() -> {
      ErpBillInfo erpBillInfo = erpBillInfoMapper.selectOne(Wrappers.<ErpBillInfo>lambdaQuery()
          .eq(ErpBillInfo::getOrderNo, req.getOrderNo()).last(" limit 1 "));
      responseAtomic.get()
          .setErpBillInfo(BeanUtil.toBean(erpBillInfo, com.yxt.order.common.base_order_dto.ErpBillInfo.class));
    }, orderSearchSubPool));
    //下账配置
    futureList.add(CompletableFuture.runAsync(() -> {
      InnerStoreDictionary innerStoreDictionary = innerStoreDictionaryMapper.selectOne(Wrappers.<InnerStoreDictionary>lambdaQuery()
          .eq(InnerStoreDictionary::getOrganizationCode, orderInfo.getOrganizationCode())
          .last(" limit 1 "));
      responseAtomic.get()
          .setInnerStoreDictionary(BeanUtil.toBean(innerStoreDictionary, com.yxt.order.common.base_order_dto.InnerStoreDictionary.class));
    }, orderSearchSubPool));
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return responseAtomic.get();
  }
}
