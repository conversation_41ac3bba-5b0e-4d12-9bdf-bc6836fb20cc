package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.ErpRefundInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundCheckMapper;
import cn.hydee.middle.business.order.mapper.RefundDetailMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.mapper.RefundPictureMapper;
import cn.hydee.middle.business.order.service.RefundSearchService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RefundSearchServiceImpl implements RefundSearchService {

  @Autowired
  private RefundOrderMapper refundOrderMapper;
  @Autowired
  private RefundDetailMapper refundDetailMapper;
  @Autowired
  private RefundCheckMapper refundCheckMapper;
  @Autowired
  private RefundPictureMapper refundPictureMapper;
  @Autowired
  private ErpRefundInfoMapper erpRefundInfoMapper;

  @Override
  public PageDTO<RefundSimpleRes> refundSearchPage(RefundPageSearchReq req) {
    // 构建查询条件
    LambdaQueryWrapper<RefundOrder> queryWrapper = Wrappers.lambdaQuery();

    // 订单号列表过滤
    if (CollUtil.isNotEmpty(req.getOrderNoList())) {
      List<Long> orderNos = req.getOrderNoList().stream()
          .map(Long::valueOf)
          .collect(Collectors.toList());
      queryWrapper.in(RefundOrder::getOrderNo, orderNos);
    }

    // 退款单号列表过滤
    if (CollUtil.isNotEmpty(req.getRefundNoList())) {
      List<Long> refundNos = req.getRefundNoList().stream()
          .map(Long::valueOf)
          .collect(Collectors.toList());
      queryWrapper.in(RefundOrder::getRefundNo, refundNos);
    }

    // 网店编码列表过滤
    if (CollUtil.isNotEmpty(req.getClientCodeList())) {
      queryWrapper.in(RefundOrder::getClientCode, req.getClientCodeList());
    }

    // 门店编码列表过滤
    if (CollUtil.isNotEmpty(req.getOrgCodeList())) {
      queryWrapper.in(RefundOrder::getOrganizationCode, req.getOrgCodeList());
    }

    // 退款状态列表过滤
    if (CollUtil.isNotEmpty(req.getRefundStatusList())) {
      queryWrapper.in(RefundOrder::getState, req.getRefundStatusList());
    }

    // 平台编码过滤
    if (StrUtil.isNotBlank(req.getPlatformCode())) {
      queryWrapper.eq(RefundOrder::getThirdPlatformCode, req.getPlatformCode());
    }

    // 服务模式过滤
    if (StrUtil.isNotBlank(req.getServiceMode())) {
      queryWrapper.eq(RefundOrder::getServiceMode, req.getServiceMode());
    }

    // 创建时间范围过滤
    if (req.getStartRefundCreatedDate() != null) {
      LocalDateTime startDateTime = LocalDateTime.of(req.getStartRefundCreatedDate(), LocalTime.MIN);
      queryWrapper.ge(RefundOrder::getCreateTime, startDateTime);
    }

    if (req.getEndRefundCreatedDate() != null) {
      LocalDateTime endDateTime = LocalDateTime.of(req.getEndRefundCreatedDate(), LocalTime.MAX);
      queryWrapper.le(RefundOrder::getCreateTime, endDateTime);
    }

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(RefundOrder::getCreateTime);

    // 执行分页查询 - 使用请求中的分页参数
    Page<RefundOrder> page = new Page<>(req.getCurrentPage(), req.getPageSize());
    IPage<RefundOrder> refundOrderPage = refundOrderMapper.selectPage(page, queryWrapper);

    // 构建返回结果
    PageDTO<RefundSimpleRes> pageDTO = new PageDTO<>();
    pageDTO.setCurrentPage(req.getCurrentPage());
    pageDTO.setPageSize(req.getPageSize());
    pageDTO.setTotalCount(refundOrderPage.getTotal());
    pageDTO.setTotalPage(refundOrderPage.getPages());
    // 转换数据
    List<RefundSimpleRes> simpleRefundList = refundOrderPage.getRecords().stream()
        .map(refundOrder -> {
          RefundSimpleRes response = new RefundSimpleRes();
          response.setOrderNo(StrUtil.toStringOrNull(refundOrder.getOrderNo()));
          response.setRefundNo(StrUtil.toStringOrNull(refundOrder.getRefundNo()));
          response.setPlatformCode(refundOrder.getThirdPlatformCode());
          response.setBusinessType(refundOrder.getServiceMode());
          return response;
        }).collect(Collectors.toList());

    pageDTO.setData(simpleRefundList);
    return pageDTO;
  }

  @Override
  public RefundDomainRelatedRes refundSearchByRefundNo(RefundSearchByRefundNoReq req) {
    RefundDomainRelatedRes response = new RefundDomainRelatedRes();

    // 参数验证
    if (req == null || StrUtil.isBlank(req.getRefundNo())) {
      return response;
    }

    try {
      // 转换退款单号
      Long refundNo = Long.valueOf(req.getRefundNo());

      // 1. 查询退款单基本信息
      RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(refundNo);
      if (refundOrder == null) {
        return response;
      }

      // 转换并设置退款单信息
      com.yxt.order.common.base_order_dto.RefundOrder domainRefundOrder = convertToRefundOrderDto(refundOrder);
      response.setRefundOrder(domainRefundOrder);

      // 2. 查询退款明细列表
      List<cn.hydee.middle.business.order.entity.RefundDetail> refundDetailList =
          refundDetailMapper.selectListByRefundNo(refundNo);
      if (CollUtil.isNotEmpty(refundDetailList)) {
        List<com.yxt.order.common.base_order_dto.RefundDetail> domainRefundDetailList =
            refundDetailList.stream()
                .map(this::convertToRefundDetailDto)
                .collect(Collectors.toList());
        response.setRefundDetailList(domainRefundDetailList);
      }

      // 3. 查询退款审核记录列表
      List<cn.hydee.middle.business.order.entity.RefundCheck> refundCheckList =
          refundCheckMapper.selectListByRefundNo(refundNo);
      if (CollUtil.isNotEmpty(refundCheckList)) {
        List<com.yxt.order.common.base_order_dto.RefundCheck> domainRefundCheckList =
            refundCheckList.stream()
                .map(this::convertToRefundCheckDto)
                .collect(Collectors.toList());
        response.setRefundCheckList(domainRefundCheckList);
      }

      // 4. 查询ERP退款信息
      cn.hydee.middle.business.order.entity.ErpRefundInfo erpRefundInfo =
          erpRefundInfoMapper.selectByRefundNo(refundNo);
      if (erpRefundInfo != null) {
        com.yxt.order.common.base_order_dto.ErpRefundInfo domainErpRefundInfo =
            convertToErpRefundInfoDto(erpRefundInfo);
        response.setErpRefundInfo(domainErpRefundInfo);
      }

      // 5. 查询退款图片列表
      List<cn.hydee.middle.business.order.entity.RefundPicture> refundPictureList =
          refundPictureMapper.selectListByRefundNo(refundNo);
      if (CollUtil.isNotEmpty(refundPictureList)) {
        List<String> picUrlList = refundPictureList.stream()
            .map(cn.hydee.middle.business.order.entity.RefundPicture::getPicture)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toList());
        response.setRefundPicList(picUrlList);
      }

    } catch (NumberFormatException e) {
      // 退款单号格式不正确，返回空结果
      return response;
    } catch (Exception e) {
      // 数据库查询异常，返回空结果
      return response;
    }

    return response;
  }

  /**
   * 转换RefundOrder实体为Domain DTO
   */
  private com.yxt.order.common.base_order_dto.RefundOrder convertToRefundOrderDto(RefundOrder entity) {
    if (entity == null) {
      return null;
    }

    try {
      // 使用JSON序列化反序列化进行对象转换
      String json = JSONUtil.toJsonStr(entity);
      return JSONUtil.toBean(json, com.yxt.order.common.base_order_dto.RefundOrder.class);
    } catch (Exception e) {
      // 转换失败时返回null
      return null;
    }
  }

  /**
   * 转换RefundDetail实体为Domain DTO
   */
  private com.yxt.order.common.base_order_dto.RefundDetail convertToRefundDetailDto(
      cn.hydee.middle.business.order.entity.RefundDetail entity) {
    if (entity == null) {
      return null;
    }

    try {
      // 使用JSON序列化反序列化进行对象转换
      String json = JSONUtil.toJsonStr(entity);
      return JSONUtil.toBean(json, com.yxt.order.common.base_order_dto.RefundDetail.class);
    } catch (Exception e) {
      // 转换失败时返回null
      return null;
    }
  }

  /**
   * 转换RefundCheck实体为Domain DTO
   */
  private com.yxt.order.common.base_order_dto.RefundCheck convertToRefundCheckDto(
      cn.hydee.middle.business.order.entity.RefundCheck entity) {
    if (entity == null) {
      return null;
    }

    try {
      // 使用JSON序列化反序列化进行对象转换
      String json = JSONUtil.toJsonStr(entity);
      return JSONUtil.toBean(json, com.yxt.order.common.base_order_dto.RefundCheck.class);
    } catch (Exception e) {
      // 转换失败时返回null
      return null;
    }
  }

  /**
   * 转换ErpRefundInfo实体为Domain DTO
   */
  private com.yxt.order.common.base_order_dto.ErpRefundInfo convertToErpRefundInfoDto(
      cn.hydee.middle.business.order.entity.ErpRefundInfo entity) {
    if (entity == null) {
      return null;
    }

    try {
      // 使用JSON序列化反序列化进行对象转换
      String json = JSONUtil.toJsonStr(entity);
      return JSONUtil.toBean(json, com.yxt.order.common.base_order_dto.ErpRefundInfo.class);
    } catch (Exception e) {
      // 转换失败时返回null
      return null;
    }
  }
}
