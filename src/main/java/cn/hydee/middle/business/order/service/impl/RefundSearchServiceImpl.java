package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.service.RefundSearchService;
import cn.hydee.middle.business.order.util.PageTool;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.lang.dto.api.PageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RefundSearchServiceImpl implements RefundSearchService {

  @Autowired
  private RefundOrderMapper refundOrderMapper;

  @Override
  public PageDTO<RefundSimpleRes> refundSearchPage(RefundPageSearchReq req) {
    // 构建查询条件
    LambdaQueryWrapper<RefundOrder> queryWrapper = Wrappers.lambdaQuery();

    // 订单号列表过滤
    if (CollUtil.isNotEmpty(req.getOrderNoList())) {
      List<Long> orderNos = req.getOrderNoList().stream()
          .map(Long::valueOf)
          .collect(Collectors.toList());
      queryWrapper.in(RefundOrder::getOrderNo, orderNos);
    }

    // 退款单号列表过滤
    if (CollUtil.isNotEmpty(req.getRefundNoList())) {
      List<Long> refundNos = req.getRefundNoList().stream()
          .map(Long::valueOf)
          .collect(Collectors.toList());
      queryWrapper.in(RefundOrder::getRefundNo, refundNos);
    }

    // 网店编码列表过滤
    if (CollUtil.isNotEmpty(req.getClientCodeList())) {
      queryWrapper.in(RefundOrder::getClientCode, req.getClientCodeList());
    }

    // 门店编码列表过滤
    if (CollUtil.isNotEmpty(req.getOrgCodeList())) {
      queryWrapper.in(RefundOrder::getOrganizationCode, req.getOrgCodeList());
    }

    // 退款状态列表过滤
    if (CollUtil.isNotEmpty(req.getRefundStatusList())) {
      queryWrapper.in(RefundOrder::getState, req.getRefundStatusList());
    }

    // 平台编码过滤
    if (StrUtil.isNotBlank(req.getPlatformCode())) {
      queryWrapper.eq(RefundOrder::getThirdPlatformCode, req.getPlatformCode());
    }

    // 服务模式过滤
    if (StrUtil.isNotBlank(req.getServiceMode())) {
      queryWrapper.eq(RefundOrder::getServiceMode, req.getServiceMode());
    }

    // 创建时间范围过滤
    if (req.getStartRefundCreatedDate() != null) {
      LocalDateTime startDateTime = LocalDateTime.of(req.getStartRefundCreatedDate(), LocalTime.MIN);
      queryWrapper.ge(RefundOrder::getCreateTime, startDateTime);
    }

    if (req.getEndRefundCreatedDate() != null) {
      LocalDateTime endDateTime = LocalDateTime.of(req.getEndRefundCreatedDate(), LocalTime.MAX);
      queryWrapper.le(RefundOrder::getCreateTime, endDateTime);
    }

    // 按创建时间倒序排序
    queryWrapper.orderByDesc(RefundOrder::getCreateTime);

    // 执行分页查询 - 使用请求中的分页参数
    Page<RefundOrder> page = new Page<>(req.getCurrentPage(), req.getPageSize());
    IPage<RefundOrder> refundOrderPage = refundOrderMapper.selectPage(page, queryWrapper);

    // 构建返回结果
    PageDTO<RefundSimpleRes> pageDTO = new PageDTO<>();
    pageDTO.setCurrentPage(req.getCurrentPage());
    pageDTO.setPageSize(req.getPageSize());
    pageDTO.setTotalCount(refundOrderPage.getTotal());
    pageDTO.setTotalPage(refundOrderPage.getPages());
    // 转换数据
    List<RefundSimpleRes> simpleRefundList = refundOrderPage.getRecords().stream()
        .map(refundOrder -> {
          RefundSimpleRes response = new RefundSimpleRes();
          response.setOrderNo(StrUtil.toStringOrNull(refundOrder.getOrderNo()));
          response.setRefundNo(StrUtil.toStringOrNull(refundOrder.getRefundNo()));
          response.setPlatformCode(refundOrder.getThirdPlatformCode());
          response.setBusinessType(refundOrder.getServiceMode());
          return response;
        }).collect(Collectors.toList());

    pageDTO.setData(simpleRefundList);
    return pageDTO;
  }

  @Override
  public RefundDomainRelatedRes refundSearchByRefundNo(RefundSearchByRefundNoReq req) {
    RefundDomainRelatedRes response = new RefundDomainRelatedRes();

    if (StrUtil.isBlank(req.getRefundNo())) {
      return response;
    }

    try {
      Long refundNo = Long.valueOf(req.getRefundNo());
      RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(refundNo);

      if (refundOrder != null) {
        // TODO: 根据需要填充RefundDomainRelatedRes的具体字段
        // 这里可以根据业务需求添加更多的退款单相关信息
      }
    } catch (NumberFormatException e) {
      // 退款单号格式不正确，返回空结果
    }

    return response;
  }
}
