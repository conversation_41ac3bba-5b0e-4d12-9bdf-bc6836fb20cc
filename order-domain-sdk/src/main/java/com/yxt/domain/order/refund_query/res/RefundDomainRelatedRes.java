package com.yxt.domain.order.refund_query.res;

import com.yxt.order.common.base_order_dto.ErpRefundInfo;
import com.yxt.order.common.base_order_dto.RefundCheck;
import com.yxt.order.common.base_order_dto.RefundDetail;
import com.yxt.order.common.base_order_dto.RefundOrder;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RefundDomainRelatedRes {

  private RefundOrder refundOrder;

  private List<RefundDetail> refundDetailList;

  private List<RefundCheck> refundCheckList;

  private ErpRefundInfo erpRefundInfo;

  private List<String> refundPicList;

}
